package com.cdkit.modules.cm.domain.project.mode.entity;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

import lombok.Data;
import com.cdkitframework.poi.excel.annotation.Excel;
import com.cdkitframework.poi.excel.annotation.ExcelCollection;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.util.Date;
import com.cdkit.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;


/**
 * @Description: 项目计划域
 * @Author: cdkit-boot
 * @Date:   2025-07-18
 * @Version: V1.0
 */
@Data
@Schema(description="cost_project_planPage对象")
public class CostProjectPlanEntity {

	/**UUID主键*/
	@Schema(description = "UUID主键")
    private String id;
	/**计划编号(JH+8位日期+3位流水)*/
	@Excel(name = "计划编号(JH+8位日期+3位流水)", width = 15)
	@Schema(description = "计划编号(JH+8位日期+3位流水)")
    private String planCode;
	/**计划名称*/
	@Excel(name = "计划名称", width = 15)
	@Schema(description = "计划名称")
    private String planName;
	/**关联父计划id*/
	@Excel(name = "关联父计划id", width = 15)
	@Schema(description = "关联父计划id")
	private String parentPlanId;
	/**年度预算ID*/
	@Excel(name = "年度预算ID", width = 15)
	@Schema(description = "年度预算ID")
	private String annualBudgetId;
	/**项目类型（market_project市场项目、non_market_project非市场项目）*/
	@Excel(name = "项目类型", width = 15, dicCode = "cost_project_type")
	@Dict(dicCode = "cost_project_type")
	@Schema(description = "项目类型（market_project市场项目、non_market_project非市场项目）")
	private String projectType;
	/**状态(PENDING_SUBMIT/APPROVING/LOCKED)*/
	@Excel(name = "状态(PENDING_SUBMIT/APPROVING/LOCKED)", width = 15, dicCode = "cost_project_plan_status")
    @Dict(dicCode = "cost_project_plan_status")
	@Schema(description = "状态(PENDING_SUBMIT/APPROVING/LOCKED)")
    private String projectPlanStatus;

	@Schema(description = "状态名称")
	private String projectPlanStatus_dictText;
    /**工作流实例ID*/
    @Schema(description = "工作流实例ID")
    private String wiid;
	/**项目编号*/
	@Excel(name = "项目编号", width = 15)
	@Schema(description = "项目编号")
    private String projectCode;
	/**项目名称*/
	@Excel(name = "项目名称", width = 15)
	@Schema(description = "项目名称")
    private String projectName;
	/**计划类型(年度计划/季度计划 ANNUAL/QUARTERLY)*/
	@Excel(name = "计划类型(年度计划/季度计划 ANNUAL/QUARTERLY)", width = 15, dicCode = "cost_plan_type")
    @Dict(dicCode = "cost_plan_type")
	@Schema(description = "计划类型(年度计划/季度计划 ANNUAL/QUARTERLY)")
    private String planType;
	/**中心*/
	@Excel(name = "中心", width = 15)
	@Schema(description = "中心")
    private String center;
	/**项目组*/
	@Excel(name = "项目组", width = 15)
	@Schema(description = "项目组")
    private String projectGroup;
	/**合同模式*/
	@Excel(name = "合同模式", width = 15, dicCode = "cost_contact_mode")
    @Dict(dicCode = "cost_contact_mode")
	@Schema(description = "合同模式")
    private String contractMode;
	/**合同编号*/
	@Excel(name = "合同编号", width = 15)
	@Schema(description = "合同编号")
    private String contractCode;
	/**合同名称*/
	@Excel(name = "合同名称", width = 15)
	@Schema(description = "合同名称")
    private String contractName;
	/**合同/预估收入(税后万元)*/
	@Excel(name = "合同/预估收入(税后万元)", width = 15)
	@Schema(description = "合同/预估收入(税后万元)")
    private java.math.BigDecimal contractRevenue;
	/**直接成本小计*/
	@Excel(name = "直接成本小计", width = 15)
	@Schema(description = "直接成本小计")
    private java.math.BigDecimal directCostTotal;
	/**其他成本小计*/
	@Excel(name = "其他成本小计", width = 15)
	@Schema(description = "其他成本小计")
    private java.math.BigDecimal otherCostTotal;
	/**税金及附加小计*/
	@Excel(name = "税金及附加小计", width = 15)
	@Schema(description = "税金及附加小计")
    private java.math.BigDecimal taxCostTotal;
	/**成本总计*/
	@Excel(name = "成本总计", width = 15)
	@Schema(description = "成本总计")
    private java.math.BigDecimal costTotal;
	/**项目利润(万元)*/
	@Excel(name = "项目利润(万元)", width = 15)
	@Schema(description = "项目利润(万元)")
    private java.math.BigDecimal projectProfit;
	/**利润率(%)*/
	@Excel(name = "利润率(%)", width = 15)
	@Schema(description = "利润率(%)")
    private java.math.BigDecimal profitMargin;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@Schema(description = "创建时间")
    private Date createTime;
	/**创建人*/
	@Schema(description = "创建人")
    private String createBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@Schema(description = "更新时间")
    private Date updateTime;
	/**更新人*/
	@Schema(description = "更新人")
    private String updateBy;
	/**租户ID*/
	@Excel(name = "租户ID", width = 15)
	@Schema(description = "租户ID")
    private Integer tenantId;
	/**删除标识 0:未删除 1:删除*/
	@Excel(name = "删除标识 0:未删除 1:删除", width = 15)
	@Schema(description = "删除标识 0:未删除 1:删除")
    private Integer delFlag;
	/**所属部门代码*/
	@Schema(description = "所属部门代码")
    private String sysOrgCode;

	@ExcelCollection(name="项目计划明细")
	@Schema(description = "项目计划明细")
	private List<CostProjectPlanDetailEntity> costProjectPlanDetailList;
	@ExcelCollection(name="直接成本明细")
	@Schema(description = "直接成本明细")
	private List<CostDirectCostEntity> costDirectCostList;
	@ExcelCollection(name="其他成本明细")
	@Schema(description = "其他成本明细")
	private List<CostOtherCostEntity> costOtherCostList;
	@ExcelCollection(name="税金及附加明细")
	@Schema(description = "税金及附加明细")
	private List<CostTaxCostEntity> costTaxCostList;
	@ExcelCollection(name="原料明细")
	@Schema(description = "原料明细")
	private List<CostMaterialDetailEntity> costMaterialDetailList;

	/**
	 * 计算项目计划预算依据
	 * 根据业务规则计算各项成本和利润指标
	 */
	public void calculateBudgetBasis() {
		// 计算明细数据
		calculatePlanDetails();

		// 计算汇总数据
		calculateSummaryData();
	}

	/**
	 * 计算项目计划明细数据
	 */
	private void calculatePlanDetails() {
		if (costProjectPlanDetailList == null || costProjectPlanDetailList.isEmpty()) {
			return;
		}

		for (CostProjectPlanDetailEntity detail : costProjectPlanDetailList) {
			// 计算年度预算需求吨 = 密度 × 用量
			if (detail.getDensity() != null && detail.getUsageAmount() != null) {
				BigDecimal demandTon = detail.getDensity().multiply(detail.getUsageAmount())
					.setScale(4, RoundingMode.HALF_UP);
				detail.setDemandTon(demandTon);
			}

			// 计算年度预算应收(油，万元) = 预计年处理量(油，方) × 费率 ÷ 10000
			if (detail.getEstimatedAnnualOil() != null && detail.getFeeRate() != null) {
				BigDecimal revenueOil = detail.getEstimatedAnnualOil()
					.multiply(detail.getFeeRate())
					.divide(new BigDecimal("10000"), 6, RoundingMode.HALF_UP);
				detail.setRevenueOil(revenueOil);
			}

			// 计算年度预算应收(水，万元) = 预计年处理量(水，方) × 费率 ÷ 10000
			if (detail.getEstimatedAnnualWater() != null && detail.getFeeRate() != null) {
				BigDecimal revenueWater = detail.getEstimatedAnnualWater()
					.multiply(detail.getFeeRate())
					.divide(new BigDecimal("10000"), 6, RoundingMode.HALF_UP);
				detail.setRevenueWater(revenueWater);
			}
		}
	}

	/**
	 * 计算汇总数据
	 */
	private void calculateSummaryData() {
		// 计算直接成本小计 - 材料成本不含税总价累加
		BigDecimal directCostTotal = BigDecimal.ZERO;
		if (costDirectCostList != null && !costDirectCostList.isEmpty()) {
			for (CostDirectCostEntity directCost : costDirectCostList) {
				if (directCost.getTotalExcludingTax() != null) {
					directCostTotal = directCostTotal.add(directCost.getTotalExcludingTax());
				}
			}
		}

		this.directCostTotal = directCostTotal.setScale(2, RoundingMode.HALF_UP);

		// 计算其他成本小计
		BigDecimal otherCostTotal = BigDecimal.ZERO;
		if (costOtherCostList != null && !costOtherCostList.isEmpty()) {
			for (CostOtherCostEntity otherCost : costOtherCostList) {
				if (otherCost.getFeeAmount() != null) {
					otherCostTotal = otherCostTotal.add(otherCost.getFeeAmount());
				}
			}
		}
		this.otherCostTotal = otherCostTotal.setScale(2, RoundingMode.HALF_UP);

		// 计算税金及附加小计
		BigDecimal taxCostTotal = BigDecimal.ZERO;
		if (costTaxCostList != null && !costTaxCostList.isEmpty()) {
			for (CostTaxCostEntity taxCost : costTaxCostList) {
				if (taxCost.getFeeAmount() != null) {
					taxCostTotal = taxCostTotal.add(taxCost.getFeeAmount());
				}
			}
		}
		this.taxCostTotal = taxCostTotal.setScale(2, RoundingMode.HALF_UP);

		// 计算成本总计 = 直接成本小计 + 其他成本小计 + 税金及附加小计
		BigDecimal costTotal = this.directCostTotal.add(this.otherCostTotal).add(this.taxCostTotal);
		this.costTotal = costTotal.setScale(2, RoundingMode.HALF_UP);

		// 计算年度预算应收总计（油+水）
		BigDecimal totalRevenue = BigDecimal.ZERO;
		if (costProjectPlanDetailList != null && !costProjectPlanDetailList.isEmpty()) {
			for (CostProjectPlanDetailEntity detail : costProjectPlanDetailList) {
				if (detail.getRevenueOil() != null) {
					totalRevenue = totalRevenue.add(detail.getRevenueOil());
				}
				if (detail.getRevenueWater() != null) {
					totalRevenue = totalRevenue.add(detail.getRevenueWater());
				}
			}
		}

		// 计算项目利润 = 年度预算应收总计 - 成本总计
		BigDecimal projectProfit = totalRevenue.subtract(this.costTotal);
		this.projectProfit = projectProfit.setScale(2, RoundingMode.HALF_UP);

		// 计算利润率 = 项目利润 / 成本总计
		if (this.costTotal.compareTo(BigDecimal.ZERO) != 0) {
			BigDecimal profitMargin = projectProfit.divide(this.costTotal, 4, RoundingMode.HALF_UP)
				.multiply(new BigDecimal("100"));
			this.profitMargin = profitMargin.setScale(2, RoundingMode.HALF_UP);
		} else {
			this.profitMargin = BigDecimal.ZERO;
		}
	}

	/**
	 * 判断是否可以编辑
	 * @return true-可以编辑，false-不可以编辑
	 */
	public boolean canEdit() {
		return !"LOCKED".equals(this.projectPlanStatus);
	}

	/**
	 * 判断是否可以计算
	 * @return true-可以计算，false-不可以计算
	 */
	public boolean canCalculate() {
		return costProjectPlanDetailList != null && !costProjectPlanDetailList.isEmpty();
	}

}
