package com.cdkit.modules.cm.domain.gateway.plm.entity;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 产品配方信息
 * 包含第一层的配方信息和最底层的物料列表
 * <AUTHOR>
 * @date 2025/07/18
 */
@Data
@Accessors(chain = true)
public class ProductRecipeInfo implements Serializable {

    /**
     * 配方名称（来自第一层的name字段）
     */
    private String recipeName;

    /**
     * 配方编号（来自第一层的materialCode字段）
     */
    private String recipeCode;

    /**
     * 第一层完整信息
     */
    private ProductFileTreeEntity firstLevelInfo;

    /**
     * 最底层物料列表（所有叶子节点）
     */
    private List<ProductFileTreeEntity> bottomLevelMaterials;

    /**
     * 最底层物料编码列表（便于快速获取）
     */
    private List<String> bottomLevelMaterialCodes;

    /**
     * 半成品列表（所有partType=SEMI的节点，不限制为叶子节点）
     */
    private List<ProductFileTreeEntity> semiProducts;

    /**
     * 半成品编码列表（便于快速获取）
     */
    private List<String> semiProductCodes;
}
