package com.cdkit.modules.cm.domain.outsourcing.service;

import com.alibaba.fastjson2.reader.ObjectReader4;
import com.cdkit.modules.cm.domain.outsourcing.entity.OutsourcingTransferEntity;
import com.cdkit.modules.cm.domain.outsourcing.entity.OutsourcingOrderEntity;
import com.cdkit.modules.cm.domain.outsourcing.repository.OutsourcingOrderRepository;
import com.cdkit.modules.cm.domain.project.mode.entity.CostDirectCostEntity;
import com.cdkit.modules.cm.domain.project.mode.entity.CostProjectPlanEntity;
import com.cdkit.modules.cm.domain.gateway.plm.entity.ProductFileTreeEntity;
import com.cdkit.modules.cm.domain.gateway.plm.entity.ProductRecipeInfo;
import com.cdkit.modules.cm.domain.gateway.plm.ProductFileGateway;
import com.cdkit.modules.cm.domain.gateway.outsourcing.OutsourcingExternalGateway;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 转外委领域服务
 * 处理转外委相关的核心业务逻辑
 * 
 * <AUTHOR>
 * @date 2025-08-13
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class OutsourcingTransferDomainService {

    private final OutsourcingOrderRepository outsourcingOrderRepository;
    private final ProductFileGateway productFileGateway;
    private final OutsourcingExternalGateway outsourcingExternalGateway;

    /**
     * 构建转外委实体列表
     * 根据项目计划的直接成本明细构建产品和半成品的转外委实体
     * 
     * @param projectPlan 项目计划实体
     * @return 转外委实体列表
     */
    public List<OutsourcingTransferEntity> buildOutsourcingTransferEntities(CostProjectPlanEntity projectPlan) {
        log.info("构建转外委实体列表，项目计划ID: {}", projectPlan.getId());

        List<OutsourcingTransferEntity> entities = new ArrayList<>();
        List<CostDirectCostEntity> directCostList = projectPlan.getCostDirectCostList();
        
        if (directCostList == null || directCostList.isEmpty()) {
            log.warn("项目计划没有直接成本明细，planId: {}", projectPlan.getId());
            return entities;
        }

        // 批量查询已转量，提高性能
        Map<String, BigDecimal> transferredAmountMap = batchGetTransferredAmounts(
                projectPlan.getId(), directCostList);

        for (CostDirectCostEntity directCost : directCostList) {
            // 创建产品实体
            OutsourcingTransferEntity productEntity = buildProductEntity(
                    directCost, projectPlan.getId(), transferredAmountMap);

            // 获取产品对应的半成品实体
            List<OutsourcingTransferEntity> semiEntities = buildSemiProductEntities(
                    directCost, projectPlan.getId(), transferredAmountMap);
            
            if (!semiEntities.isEmpty()) {
                productEntity.setChildren(semiEntities);
            }

            entities.add(productEntity);
        }

        log.info("构建转外委实体列表完成，项目计划ID: {}, 产品数量: {}", 
                projectPlan.getId(), entities.size());
        return entities;
    }

    /**
     * 构建产品转外委实体
     */
    private OutsourcingTransferEntity buildProductEntity(
            CostDirectCostEntity directCost, String planId, Map<String, BigDecimal> transferredAmountMap) {
        
        BigDecimal transferredAmount = transferredAmountMap.getOrDefault(
                directCost.getMaterialCode(), BigDecimal.ZERO);

        OutsourcingTransferEntity entity = new OutsourcingTransferEntity()
                .setPlanId(planId)
                .setMaterialCode(directCost.getMaterialCode()) // 使用物料编码
                .setEstimatedUsage(directCost.getEstimatedUsage())
                .setFormulaName(null)
                .setFormulaCode(null)
                .setTransferredAmount(transferredAmount)
                .setOutsourcingAmount(BigDecimal.ZERO)
                .setIsProduct(true)
                .setDirectCostId(directCost.getId())
                .setMaterialCode(directCost.getMaterialCode()) // 物料编码
                .setMaterialName(directCost.getMaterialName()) // 物料名称
                .setItemType("PRODUCT");

        // 计算可转量
        entity.calculateAvailableAmount(BigDecimal.ZERO);
        return entity;
    }

    /**
     * 构建半成品转外委实体列表
     */
    private List<OutsourcingTransferEntity> buildSemiProductEntities(
            CostDirectCostEntity directCost, String planId, Map<String, BigDecimal> transferredAmountMap) {
        
        List<OutsourcingTransferEntity> semiEntities = new ArrayList<>();

        try {
            // 获取产品配方信息
            List<ProductFileTreeEntity> productFileTree = productFileGateway.getProductFileTree(directCost.getMaterialCode());
            ProductRecipeInfo recipeInfo = buildProductRecipeInfo(productFileTree);
            if (recipeInfo == null || recipeInfo.getSemiProducts() == null) {
                log.warn("未找到产品配方信息或半成品为空，产品: {}", directCost.getMaterialCode());
                return semiEntities;
            }

            // 调试：打印所有半成品信息
            log.info("=== 调试半成品数据 - 产品: {} ===", directCost.getMaterialCode());
            for (ProductFileTreeEntity semiProduct : recipeInfo.getSemiProducts()) {
                log.info("半成品编码: {}, 半成品名称: {}, partType: {}, itemType: {}",
                    semiProduct.getMaterialCode(), semiProduct.getMaterialName(),
                    semiProduct.getPartType(), semiProduct.getItemType());
            }

            // 直接使用半成品列表
            List<ProductFileTreeEntity> semiMaterials = recipeInfo.getSemiProducts();

            log.info("获取结果：找到 {} 个半成品", semiMaterials.size());

            for (ProductFileTreeEntity semiMaterial : semiMaterials) {
                OutsourcingTransferEntity semiEntity = buildSemiProductEntity(
                        semiMaterial, directCost, planId, transferredAmountMap);
                semiEntities.add(semiEntity);
            }

        } catch (Exception e) {
            log.error("构建半成品实体失败，产品: {}", directCost.getMaterialCode(), e);
        }

        return semiEntities;
    }

    /**
     * 构建半成品转外委实体
     */
    private OutsourcingTransferEntity buildSemiProductEntity(
            ProductFileTreeEntity semiMaterial, CostDirectCostEntity parentDirectCost, 
            String planId, Map<String, BigDecimal> transferredAmountMap) {

        // 计算半成品预计用量 = 产品用量 * 配方比例
        BigDecimal recipeRatio = semiMaterial.getStandardQuantity() != null ?
                semiMaterial.getStandardQuantity() : BigDecimal.ONE;
        
        BigDecimal semiEstimatedUsage = BigDecimal.ZERO;
        if (parentDirectCost.getEstimatedUsage() != null) {
            semiEstimatedUsage = parentDirectCost.getEstimatedUsage().multiply(recipeRatio);
        }

        BigDecimal transferredAmount = transferredAmountMap.getOrDefault(
                semiMaterial.getMaterialCode(), BigDecimal.ZERO);

        OutsourcingTransferEntity semiEntity = new OutsourcingTransferEntity()
                .setPlanId(planId)
                .setMaterialCode(semiMaterial.getMaterialCode())
                .setOutsourcingProduct(semiMaterial.getMaterialCode()) // 半成品默认使用自身物料编码作为外委产品
                .setOutsourcingProductName(semiMaterial.getMaterialName()) // 半成品外委产品名称
                .setEstimatedUsage(semiEstimatedUsage)
                .setFormulaName(parentDirectCost.getFormulaName())
                .setFormulaCode(parentDirectCost.getFormulaCode())
                .setTransferredAmount(transferredAmount)
                .setOutsourcingAmount(BigDecimal.ZERO)
                .setIsProduct(false)
                .setParentMaterialCode(parentDirectCost.getMaterialCode())
                .setRecipeRatio(recipeRatio)
                .setDirectCostId(parentDirectCost.getId())
                .setMaterialCode(semiMaterial.getMaterialCode())
                .setMaterialName(semiMaterial.getMaterialName())
                .setItemType(semiMaterial.getPartType());

        // 计算可转量（半成品需要考虑父产品外委量的影响）
        semiEntity.calculateAvailableAmount(BigDecimal.ZERO);
        return semiEntity;
    }

    /**
     * 批量获取已转量
     * 分别查询产品和半成品的已转量
     */
    private Map<String, BigDecimal> batchGetTransferredAmounts(
            String planId, List<CostDirectCostEntity> directCostList) {
        
        Map<String, BigDecimal> transferredAmountMap = new HashMap<>();

        try {
            // 查询产品已转量
            List<String> productNames = directCostList.stream()
                    .map(CostDirectCostEntity::getMaterialCode)
                    .filter(StringUtils::hasText)
                    .collect(Collectors.toList());
            
            if (!productNames.isEmpty()) {
                Map<String, BigDecimal> productTransferredMap = outsourcingOrderRepository
                        .batchGetTransferredAmountByProducts(planId, productNames);
                transferredAmountMap.putAll(productTransferredMap);
            }

            // 查询半成品已转量
            Set<String> materialCodes = new HashSet<>();
            for (CostDirectCostEntity directCost : directCostList) {
                try {
                    List<ProductFileTreeEntity> productFileTree = productFileGateway.getProductFileTree(directCost.getMaterialCode());
                    ProductRecipeInfo recipeInfo = buildProductRecipeInfo(productFileTree);
                    if (recipeInfo != null && recipeInfo.getSemiProducts() != null) {
                        List<String> semiMaterialCodes = recipeInfo.getSemiProducts().stream()
                                .map(ProductFileTreeEntity::getMaterialCode)
                                .filter(StringUtils::hasText)
                                .collect(Collectors.toList());
                        materialCodes.addAll(semiMaterialCodes);
                    }
                } catch (Exception e) {
                    log.warn("获取产品配方失败，产品: {}", directCost.getMaterialCode(), e);
                }
            }

            if (!materialCodes.isEmpty()) {
                Map<String, BigDecimal> materialTransferredMap = outsourcingOrderRepository
                        .batchGetTransferredAmountByMaterials(planId, new ArrayList<>(materialCodes));
                transferredAmountMap.putAll(materialTransferredMap);
            }

        } catch (Exception e) {
            log.error("批量查询已转量失败，planId: {}", planId, e);
        }

        return transferredAmountMap;
    }

    /**
     * 校验转外委数据
     * 校验外委量是否超过可转量
     */
    public void validateOutsourcingTransfer(List<OutsourcingTransferEntity> entities) {
        for (OutsourcingTransferEntity entity : entities) {
            if (!entity.isOutsourcingAmountValid()) {
                throw new IllegalArgumentException(
                        String.format("物料[%s]的外委量[%s]超过可转量[%s]",
                                entity.getMaterialCode(),
                                entity.getOutsourcingAmount(),
                                entity.getAvailableAmount()));
            }

            // 递归校验子项目
            if (entity.getChildren() != null && !entity.getChildren().isEmpty()) {
                validateOutsourcingTransfer(entity.getChildren());
            }
        }
    }

    /**
     * 计算半成品可转量
     * 考虑父产品外委量对半成品可转量的影响
     */
    public void recalculateSemiProductAvailableAmounts(List<OutsourcingTransferEntity> entities) {
        for (OutsourcingTransferEntity productEntity : entities) {
            if (Boolean.TRUE.equals(productEntity.getIsProduct()) &&
                productEntity.getChildren() != null && !productEntity.getChildren().isEmpty()) {

                BigDecimal productOutsourcingAmount = productEntity.getOutsourcingAmount() != null ?
                        productEntity.getOutsourcingAmount() : BigDecimal.ZERO;

                for (OutsourcingTransferEntity semiEntity : productEntity.getChildren()) {
                    semiEntity.calculateAvailableAmount(productOutsourcingAmount);
                }
            }
        }
    }

    /**
     * 获取外委产品选项
     * 根据产品名称获取可选的外委产品列表
     *
     * @param productName 产品名称
     * @return 外委产品选项列表
     */
    public List<String> getOutsourcingProductOptions(String productName) {
        log.info("获取外委产品选项，产品名称: {}", productName);

        if (!StringUtils.hasText(productName)) {
            log.warn("产品名称为空，返回空列表");
            return List.of();
        }

        try {
            // 这里可以根据业务需求实现具体的外委产品选项获取逻辑
            // 目前返回一些示例选项，实际应该从配置或其他服务获取
            List<String> options = Arrays.asList(
                    productName + "-外委版本1",
                    productName + "-外委版本2",
                    productName + "-标准外委版本"
            );

            log.info("获取外委产品选项成功，产品名称: {}, 选项数量: {}", productName, options.size());
            return options;

        } catch (Exception e) {
            log.error("获取外委产品选项失败，产品名称: {}", productName, e);
            return List.of();
        }
    }

    /**
     * 根据外委产品更新转外委数据
     * 当用户选择外委产品后，根据外委产品的配方信息更新半成品用量等数据
     *
     * @param entity 转外委实体
     * @return 更新后的转外委实体
     */
    public OutsourcingTransferEntity updateByOutsourcingProduct(OutsourcingTransferEntity entity) {
        log.info("根据外委产品更新转外委数据，物料编码: {}, 外委产品: {}", entity.getMaterialCode(), entity.getOutsourcingProduct());

        if (entity == null || !StringUtils.hasText(entity.getOutsourcingProduct())) {
            throw new IllegalArgumentException("转外委实体或外委产品不能为空");
        }

        try {
            // 1. 查询并设置外委产品的已外委量
            BigDecimal productTransferredAmount = outsourcingOrderRepository
                    .getTransferredAmountByPlanIdAndProduct(entity.getPlanId(), entity.getOutsourcingProduct());
            entity.setTransferredAmount(productTransferredAmount != null ? productTransferredAmount : BigDecimal.ZERO);

            log.debug("设置外委产品已外委量 - 原物料: {}, 外委产品: {}, 已外委量: {}",
                    entity.getMaterialCode(), entity.getOutsourcingProduct(), entity.getTransferredAmount());

            // 2. 获取外委产品的配方信息
            List<ProductFileTreeEntity> outsourcingProductFileTree = productFileGateway
                    .getProductFileTree(entity.getOutsourcingProduct());
            ProductRecipeInfo outsourcingRecipeInfo = buildProductRecipeInfo(outsourcingProductFileTree);

            if (outsourcingRecipeInfo == null || outsourcingRecipeInfo.getSemiProducts() == null) {
                log.warn("未找到外委产品配方信息或半成品为空，外委产品: {}", entity.getOutsourcingProduct());
                return entity;
            }

            // 3. 获取外委产品的半成品
            List<ProductFileTreeEntity> outsourcingSemiMaterials = outsourcingRecipeInfo.getSemiProducts();

            // 4. 更新半成品信息
            List<OutsourcingTransferEntity> updatedChildren = new ArrayList<>();
            for (ProductFileTreeEntity outsourcingSemiMaterial : outsourcingSemiMaterials) {
                OutsourcingTransferEntity semiEntity = buildUpdatedSemiProductEntity(
                        outsourcingSemiMaterial, entity, outsourcingRecipeInfo);
                updatedChildren.add(semiEntity);
            }

            // 5. 更新实体的子项目
            entity.setChildren(updatedChildren);

            // 6. 重新计算可转量
            recalculateAvailableAmounts(entity);

            log.info("根据外委产品更新转外委数据成功，物料编码: {}, 外委产品: {}, 产品已外委量: {}, 半成品数量: {}",
                    entity.getMaterialCode(), entity.getOutsourcingProduct(), entity.getTransferredAmount(), updatedChildren.size());
            return entity;

        } catch (Exception e) {
            log.error("根据外委产品更新转外委数据失败，物料编码: {}, 外委产品: {}",
                    entity.getMaterialCode(), entity.getOutsourcingProduct(), e);
            throw new RuntimeException("根据外委产品更新转外委数据失败: " + e.getMessage());
        }
    }

    /**
     * 构建更新后的半成品转外委实体
     */
    private OutsourcingTransferEntity buildUpdatedSemiProductEntity(
            ProductFileTreeEntity outsourcingSemiMaterial, OutsourcingTransferEntity parentEntity,
            ProductRecipeInfo outsourcingRecipeInfo) {

        // 计算半成品预计用量 = 产品外委量 * 外委产品配方比例
        BigDecimal recipeRatio = outsourcingSemiMaterial.getStandardQuantity() != null ?
                outsourcingSemiMaterial.getStandardQuantity() : BigDecimal.ONE;

        BigDecimal semiEstimatedUsage = BigDecimal.ZERO;
        if (parentEntity.getOutsourcingAmount() != null) {
            semiEstimatedUsage = parentEntity.getOutsourcingAmount().multiply(recipeRatio);
        }

        // 查询半成品已转量
        BigDecimal semiTransferredAmount = outsourcingOrderRepository
                .getTransferredAmountByPlanIdAndMaterial(parentEntity.getPlanId(), outsourcingSemiMaterial.getMaterialCode());

        // 查询父级产品的已转外委量
        BigDecimal parentTransferredAmount = outsourcingOrderRepository
                .getTransferredAmountByPlanIdAndProduct(parentEntity.getPlanId(), parentEntity.getMaterialCode());

        log.debug("构建半成品实体 - 物料: {}, 半成品: {}, 半成品已转量: {}, 父产品已转量: {}, 配方比例: {}",
                parentEntity.getMaterialCode(), outsourcingSemiMaterial.getMaterialName(),
                semiTransferredAmount, parentTransferredAmount, recipeRatio);

        OutsourcingTransferEntity semiEntity = new OutsourcingTransferEntity()
                .setPlanId(parentEntity.getPlanId())
                .setMaterialCode(outsourcingSemiMaterial.getMaterialCode())
                .setOutsourcingProduct(outsourcingSemiMaterial.getMaterialCode()) // 半成品使用自身物料编码作为外委产品
                .setOutsourcingProductName(outsourcingSemiMaterial.getMaterialName()) // 半成品外委产品名称
                .setEstimatedUsage(semiEstimatedUsage)
                .setFormulaName(outsourcingRecipeInfo.getRecipeName())
                .setFormulaCode(outsourcingRecipeInfo.getRecipeCode())
                .setTransferredAmount(semiTransferredAmount)
                .setOutsourcingAmount(BigDecimal.ZERO)
                .setIsProduct(false)
                .setParentMaterialCode(parentEntity.getMaterialCode())
                .setRecipeRatio(recipeRatio)
                .setDirectCostId(parentEntity.getDirectCostId())
                .setItemType(outsourcingSemiMaterial.getPartType());

        // 计算可转量：使用父级产品的实际已转外委量，而不是用户输入的外委量
        semiEntity.calculateAvailableAmount(parentTransferredAmount != null ? parentTransferredAmount : BigDecimal.ZERO);

        return semiEntity;
    }

    /**
     * 重新计算可转量
     */
    private void recalculateAvailableAmounts(OutsourcingTransferEntity entity) {
        if (entity.getChildren() != null && !entity.getChildren().isEmpty()) {
            BigDecimal parentOutsourcingAmount = entity.getOutsourcingAmount() != null ?
                    entity.getOutsourcingAmount() : BigDecimal.ZERO;

            for (OutsourcingTransferEntity child : entity.getChildren()) {
                child.calculateAvailableAmount(parentOutsourcingAmount);
            }
        }
    }

    /**
     * 构建产品配方信息
     * 从产品档案树结构中提取配方信息
     */
    private ProductRecipeInfo buildProductRecipeInfo(List<ProductFileTreeEntity> productFileTree) {
        if (productFileTree == null || productFileTree.isEmpty()) {
            return null;
        }

        ProductRecipeInfo recipeInfo = new ProductRecipeInfo();

        // 获取第一层信息（产品本身）
        ProductFileTreeEntity firstLevel = productFileTree.get(0);
        recipeInfo.setFirstLevelInfo(firstLevel);
        recipeInfo.setRecipeName(firstLevel.getName());
        recipeInfo.setRecipeCode(firstLevel.getMaterialCode());

        // 获取所有底层物料（叶子节点）
        List<ProductFileTreeEntity> bottomLevelMaterials = new ArrayList<>();
        List<String> bottomLevelMaterialCodes = new ArrayList<>();

        collectBottomLevelMaterials(productFileTree, bottomLevelMaterials, bottomLevelMaterialCodes);

        // 获取所有半成品（partType=SEMI的节点，不限制为叶子节点）
        List<ProductFileTreeEntity> semiProducts = new ArrayList<>();
        List<String> semiProductCodes = new ArrayList<>();

        collectSemiProducts(productFileTree, semiProducts, semiProductCodes);

        recipeInfo.setBottomLevelMaterials(bottomLevelMaterials);
        recipeInfo.setBottomLevelMaterialCodes(bottomLevelMaterialCodes);
        recipeInfo.setSemiProducts(semiProducts);
        recipeInfo.setSemiProductCodes(semiProductCodes);

        return recipeInfo;
    }

    /**
     * 递归收集底层物料
     */
    private void collectBottomLevelMaterials(List<ProductFileTreeEntity> nodes,
                                           List<ProductFileTreeEntity> bottomLevelMaterials,
                                           List<String> bottomLevelMaterialCodes) {
        for (ProductFileTreeEntity node : nodes) {
            if (node.getChildren() == null || node.getChildren().isEmpty()) {
                // 叶子节点
                bottomLevelMaterials.add(node);
                if (StringUtils.hasText(node.getMaterialCode())) {
                    bottomLevelMaterialCodes.add(node.getMaterialCode());
                }
            } else {
                // 递归处理子节点
                collectBottomLevelMaterials(node.getChildren(), bottomLevelMaterials, bottomLevelMaterialCodes);
            }
        }
    }

    /**
     * 递归收集半成品（partType=SEMI的所有节点，不限制为叶子节点）
     */
    private void collectSemiProducts(List<ProductFileTreeEntity> nodes,
                                   List<ProductFileTreeEntity> semiProducts,
                                   List<String> semiProductCodes) {
        for (ProductFileTreeEntity node : nodes) {
            // 检查当前节点是否为半成品
            if ("SEMI".equals(node.getPartType())) {
                semiProducts.add(node);
                if (StringUtils.hasText(node.getMaterialCode())) {
                    semiProductCodes.add(node.getMaterialCode());
                }
            }

            // 递归处理子节点
            if (node.getChildren() != null && !node.getChildren().isEmpty()) {
                collectSemiProducts(node.getChildren(), semiProducts, semiProductCodes);
            }
        }
    }
}
