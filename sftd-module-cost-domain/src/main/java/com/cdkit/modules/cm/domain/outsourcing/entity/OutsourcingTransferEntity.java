package com.cdkit.modules.cm.domain.outsourcing.entity;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 转外委领域实体
 * 
 * <AUTHOR>
 * @date 2025-08-13
 */
@Data
@Accessors(chain = true)
public class OutsourcingTransferEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**项目计划ID*/
    private String planId;

    /**物料编码*/
    private String materialCode;

    /**对应外委产品*/
    private String outsourcingProduct;

    /**对应外委产品名称*/
    private String outsourcingProductName;

    /**预计用量（吨）*/
    private BigDecimal estimatedUsage;

    /**配方名称*/
    private String formulaName;

    /**配方编码*/
    private String formulaCode;

    /**已转量（吨）*/
    private BigDecimal transferredAmount;

    /**可转量（吨）*/
    private BigDecimal availableAmount;

    /**外委量（吨）*/
    private BigDecimal outsourcingAmount;

    /**是否为产品*/
    private Boolean isProduct;

    /**父级物料编码*/
    private String parentMaterialCode;

    /**配方比例*/
    private BigDecimal recipeRatio;

    /**直接成本ID*/
    private String directCostId;

    /**物料名称*/
    private String materialName;

    /**物料类型*/
    private String itemType;

    /**子项目列表*/
    private List<OutsourcingTransferEntity> children;

    /**
     * 计算可转量
     * 产品可转量 = 预计用量 - 已转量
     * 半成品可转量 = 预计用量 - 已转量 - 父产品外委量 * 配方比例
     */
    public void calculateAvailableAmount(BigDecimal parentOutsourcingAmount) {
        if (estimatedUsage == null) {
            this.availableAmount = BigDecimal.ZERO;
            return;
        }

        BigDecimal transferred = transferredAmount != null ? transferredAmount : BigDecimal.ZERO;
        
        if (Boolean.TRUE.equals(isProduct)) {
            // 产品可转量 = 预计用量 - 已转量
            this.availableAmount = estimatedUsage.subtract(transferred);
        } else {
            // 半成品可转量 = 预计用量 - 已转量 - 父产品外委量 * 配方比例
            BigDecimal parentImpact = BigDecimal.ZERO;
            if (recipeRatio != null && parentOutsourcingAmount != null) {
                parentImpact = parentOutsourcingAmount.multiply(recipeRatio);
            }
            this.availableAmount = estimatedUsage.subtract(transferred).subtract(parentImpact);
        }

        // 确保可转量不为负数
        if (this.availableAmount.compareTo(BigDecimal.ZERO) < 0) {
            this.availableAmount = BigDecimal.ZERO;
        }
    }

    /**
     * 校验外委量是否合法
     */
    public boolean isOutsourcingAmountValid() {
        if (outsourcingAmount == null || availableAmount == null) {
            return true;
        }
        return outsourcingAmount.compareTo(availableAmount) <= 0;
    }

    /**
     * 检查是否有外委量
     */
    public boolean hasOutsourcingAmount() {
        return outsourcingAmount != null && outsourcingAmount.compareTo(BigDecimal.ZERO) > 0;
    }
}
