package com.cdkit.modules.cm.application.outsourcing;

import com.cdkit.common.exception.CdkitCloudException;
import com.cdkit.modules.cm.api.outsourcing.dto.OutsourcingTransferItemDTO;
import com.cdkit.modules.cm.api.outsourcing.dto.OutsourcingTransferListDTO;
import com.cdkit.modules.cm.api.outsourcing.dto.OutsourcingTransferConfirmRequest;
import com.cdkit.modules.cm.application.plm.ProductFileApplication;
import com.cdkit.modules.cm.domain.outsourcing.entity.OutsourcingTransferEntity;
import com.cdkit.modules.cm.domain.outsourcing.entity.OutsourcingOrderEntity;
import com.cdkit.modules.cm.domain.outsourcing.repository.OutsourcingOrderRepository;
import com.cdkit.modules.cm.domain.outsourcing.service.OutsourcingTransferDomainService;
import com.cdkit.modules.cm.domain.project.mode.entity.CostDirectCostEntity;
import com.cdkit.modules.cm.domain.project.mode.entity.CostProjectPlanEntity;
import com.cdkit.modules.cm.domain.project.repository.CostProjectPlanRepository;
import com.cdkit.modules.cm.domain.gateway.outsourcing.OutsourcingExternalGateway;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * 转外委应用服务
 *
 * <AUTHOR>
 * @date 2025-08-13
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class OutsourcingTransferApplicationService {

    private final CostProjectPlanRepository costProjectPlanRepository;
    private final ProductFileApplication productFileApplication;
    private final OutsourcingOrderRepository outsourcingOrderRepository;
    private final OutsourcingTransferDomainService outsourcingTransferDomainService;
    private final OutsourcingExternalGateway outsourcingExternalGateway;

    /**
     * 获取转外委列表
     *
     * @param planId 项目计划ID
     * @return 转外委项目列表
     */
    public List<OutsourcingTransferItemDTO> getOutsourcingTransferList(String planId) {
        log.info("获取转外委列表，项目计划ID: {}", planId);

        if (!StringUtils.hasText(planId)) {
            throw new CdkitCloudException("项目计划ID不能为空");
        }

        // 查询项目计划详情
        CostProjectPlanEntity projectPlan = costProjectPlanRepository.queryByIdWithDetails(planId);
        if (projectPlan == null) {
            throw new CdkitCloudException("项目计划不存在，ID: " + planId);
        }

        // 获取直接成本明细（产品列表）
        List<CostDirectCostEntity> directCostList = projectPlan.getCostDirectCostList();
        if (directCostList == null || directCostList.isEmpty()) {
            log.warn("项目计划没有直接成本明细，planId: {}", planId);
            return Collections.emptyList();
        }

        // 使用领域服务构建转外委实体列表
        List<OutsourcingTransferEntity> entities = outsourcingTransferDomainService
                .buildOutsourcingTransferEntities(projectPlan);

        // 转换为DTO列表
        List<OutsourcingTransferItemDTO> items = toItemDTOList(entities);

        log.info("获取转外委列表成功，项目计划ID: {}, 产品数量: {}", planId, items.size());
        return items;
    }

    /**
     * 确认转外委
     *
     * @param items 转外委项目列表
     * @return 外委单据ID
     */
    public String confirmOutsourcingTransfer(List<OutsourcingTransferItemDTO> items) {
        log.info("确认转外委，项目数量: {}", items.size());

        // 参数校验
        validateConfirmItems(items);

        // 转换DTO为实体
        List<OutsourcingTransferEntity> entities = toEntityList(items);

        // 使用领域服务校验外委量
        outsourcingTransferDomainService.validateOutsourcingTransfer(entities);

        // 调用外委服务生成外委单据
        String outsourcingOrderId = generateOutsourcingOrder(items);

        log.info("确认转外委成功，项目数量: {}, 外委单据ID: {}", items.size(), outsourcingOrderId);
        return outsourcingOrderId;
    }

    /**
     * 根据外委产品更新转外委数据
     * 当用户选择外委产品后，根据外委产品的配方信息更新半成品用量等数据
     *
     * @param itemDTO 转外委项目数据
     * @return 更新后的转外委项目数据
     */
    public OutsourcingTransferItemDTO updateByOutsourcingProduct(OutsourcingTransferItemDTO itemDTO) {
        log.info("根据外委产品更新转外委数据，物料编码: {}, 外委产品: {}", itemDTO.getMaterialCode(), itemDTO.getOutsourcingProduct());

        if (itemDTO == null) {
            throw new CdkitCloudException("转外委项目数据不能为空");
        }

        if (!StringUtils.hasText(itemDTO.getOutsourcingProduct())) {
            throw new CdkitCloudException("外委产品不能为空");
        }

        try {
            // 获取计划编号
            String planCode = null;
            if (StringUtils.hasText(itemDTO.getPlanId())) {
                CostProjectPlanEntity projectPlan = costProjectPlanRepository.queryByIdWithDetails(itemDTO.getPlanId());
                if (projectPlan != null) {
                    planCode = projectPlan.getPlanCode();
                }
            }

            // 转换DTO为实体
            OutsourcingTransferEntity entity = toEntity(itemDTO);

            // 使用领域服务根据外委产品更新数据
            OutsourcingTransferEntity updatedEntity = outsourcingTransferDomainService
                    .updateByOutsourcingProduct(entity);

            // 转换回DTO
            OutsourcingTransferItemDTO updatedDTO = toProductDTO(updatedEntity, itemDTO.getSequence(), planCode);

            // 处理子项目（半成品）
            if (updatedEntity.getChildren() != null && !updatedEntity.getChildren().isEmpty()) {
                List<OutsourcingTransferItemDTO> children = new ArrayList<>();
                for (OutsourcingTransferEntity childEntity : updatedEntity.getChildren()) {
                    OutsourcingTransferItemDTO childDTO = toSemiProductDTO(childEntity, updatedDTO, planCode);
                    children.add(childDTO);
                }
                updatedDTO.setChildren(children);
            }

            log.info("根据外委产品更新转外委数据成功，物料编码: {}, 外委产品: {}", itemDTO.getMaterialCode(), itemDTO.getOutsourcingProduct());
            return updatedDTO;

        } catch (Exception e) {
            log.error("根据外委产品更新转外委数据失败，物料编码: {}, 外委产品: {}", itemDTO.getMaterialCode(), itemDTO.getOutsourcingProduct(), e);
            throw new CdkitCloudException("根据外委产品更新转外委数据失败: " + e.getMessage());
        }
    }











    /**
     * 创建空响应
     */
    private OutsourcingTransferListDTO createEmptyResponse(CostProjectPlanEntity projectPlan) {
        return new OutsourcingTransferListDTO().setItems(Collections.emptyList());
    }

    /**
     * 校验确认项目列表
     */
    private void validateConfirmItems(List<OutsourcingTransferItemDTO> items) {
        if (items == null || items.isEmpty()) {
            throw new CdkitCloudException("转外委项目列表不能为空");
        }

        // 检查是否有外委量
        boolean hasOutsourcingAmount = items.stream()
                .anyMatch(item -> item.getOutsourcingAmount() != null &&
                        item.getOutsourcingAmount().compareTo(BigDecimal.ZERO) > 0);

        if (!hasOutsourcingAmount) {
            throw new CdkitCloudException("请至少填写一个外委量");
        }
    }
    /**
     * 生成外委单据
     * 通过外委外部服务网关调用外委服务生成单据
     */
    private String generateOutsourcingOrder(List<OutsourcingTransferItemDTO> items) {
        log.info("生成外委单据，项目数量: {}", items.size());

        // 转换DTO为外委单据实体
        List<OutsourcingOrderEntity> orderEntities = convertToOutsourcingOrderEntities(items);

        // 调用外委服务生成单据
        return outsourcingExternalGateway.addOutsourcingOrder(orderEntities);
    }





    /**
     * 转换项目列表为外委单据实体列表
     */
    private List<OutsourcingOrderEntity> convertToOutsourcingOrderEntities(List<OutsourcingTransferItemDTO> items) {
        List<OutsourcingOrderEntity> orderEntities = new ArrayList<>();

        for (OutsourcingTransferItemDTO item : items) {
            if (item.getAvailableAmount() != null && item.getAvailableAmount().compareTo(BigDecimal.ZERO) > 0) {
                // 从第一个有效项目中获取planId，或者从item中获取
                String planCode = extractPlanIdFromItems(items);

                OutsourcingOrderEntity orderEntity = new OutsourcingOrderEntity()
                        .setUpstreamOrderCode(planCode) // 上游单据号为项目计划Code
                        .setMaterialCode(item.getMaterialCode())
                        .setMaterialName(item.getMaterialName())
                        .setItemType(item.getItemType())
                        .setFormulaName(item.getFormulaName())
                        .setFormulaCode(item.getFormulaCode());

                orderEntities.add(orderEntity);
            }
        }

        return orderEntities;
    }

    /**
     * 从项目列表中提取项目计划Code
     */
    private String extractPlanIdFromItems(List<OutsourcingTransferItemDTO> items) {
        return items.stream()
                .filter(item -> StringUtils.hasText(item.getPlanCode()))
                .map(OutsourcingTransferItemDTO::getPlanCode)
                .findFirst()
                .orElse("");
    }

    /**
     * 创建转外委列表响应DTO
     * 只返回转外委项目明细列表
     */
    private OutsourcingTransferListDTO createListDTO(List<OutsourcingTransferEntity> entities) {
        List<OutsourcingTransferItemDTO> items = toItemDTOList(entities);
        return new OutsourcingTransferListDTO().setItems(items);
    }

    /**
     * 转换转外委实体列表为DTO列表
     * 确保产品作为主表，半成品作为子表
     */
    private List<OutsourcingTransferItemDTO> toItemDTOList(List<OutsourcingTransferEntity> entities) {
        if (entities == null || entities.isEmpty()) {
            return Collections.emptyList();
        }

        // 获取项目计划信息（用于获取计划编号）
        String planId = entities.stream()
                .filter(entity -> StringUtils.hasText(entity.getPlanId()))
                .map(OutsourcingTransferEntity::getPlanId)
                .findFirst()
                .orElse(null);

        String planCode = null;
        if (StringUtils.hasText(planId)) {
            CostProjectPlanEntity projectPlan = costProjectPlanRepository.queryByIdWithDetails(planId);
            if (projectPlan != null) {
                planCode = projectPlan.getPlanCode();
            }
        }

        List<OutsourcingTransferItemDTO> productDTOList = new ArrayList<>();
        int sequence = 1;

        // 只处理产品实体（主表）
        for (OutsourcingTransferEntity entity : entities) {
            if (Boolean.TRUE.equals(entity.getIsProduct())) {
                OutsourcingTransferItemDTO productDTO = toProductDTO(entity, sequence++, planCode);

                // 转换半成品子项目
                if (entity.getChildren() != null && !entity.getChildren().isEmpty()) {
                    List<OutsourcingTransferItemDTO> semiProductDTOs = new ArrayList<>();
                    for (OutsourcingTransferEntity semiEntity : entity.getChildren()) {
                        OutsourcingTransferItemDTO semiDTO = toSemiProductDTO(semiEntity, productDTO, planCode);
                        semiProductDTOs.add(semiDTO);
                    }
                    productDTO.setChildren(semiProductDTOs);
                }

                productDTOList.add(productDTO);
            }
        }

        return productDTOList;
    }

    /**
     * 转换产品实体为产品DTO（主表）
     * 只返回产品基本信息，外委相关字段置空，用于产品选择
     */
    private OutsourcingTransferItemDTO toProductDTO(OutsourcingTransferEntity entity, int sequence, String planCode) {
        if (entity == null) {
            return null;
        }

        return new OutsourcingTransferItemDTO()
                .setSequence(sequence)
                .setMaterialCode(entity.getMaterialCode())
                .setMaterialName(entity.getMaterialName()) // 设置物料名称
                .setOutsourcingProduct(null) // 外委产品置空
                .setOutsourcingProductName(null) // 外委产品名称置空
                .setEstimatedUsage(entity.getEstimatedUsage())
                .setFormulaName(null) // 配方名称置空
                .setFormulaCode(null) // 配方编码置空
                .setTransferredAmount(null) // 已转量置空
                .setAvailableAmount(null) // 可转量置空
                .setOutsourcingAmount(null) // 外委量置空
                .setIsProduct(true) // 明确标识为产品
                .setParentMaterialCode(null) // 产品没有父级
                .setRecipeRatio(null) // 产品没有配方比例
                .setExpanded(false) // 默认不展开
                .setDirectCostId(entity.getDirectCostId())
                .setPlanId(entity.getPlanId()) // 设置项目计划ID
                .setPlanCode(planCode) // 设置计划编号
                .setItemType(entity.getItemType())
                .setMaterialOut(null); // 外委产品选项置空
    }

    /**
     * 转换半成品实体为半成品DTO（子表）
     * 只返回半成品基本信息，外委相关字段置空，用于产品选择
     */
    private OutsourcingTransferItemDTO toSemiProductDTO(OutsourcingTransferEntity semiEntity,
                                                       OutsourcingTransferItemDTO parentProductDTO,
                                                       String planCode) {
        if (semiEntity == null) {
            return null;
        }

        return new OutsourcingTransferItemDTO()
                .setSequence(null) // 半成品不需要序号，由前端处理
                .setMaterialCode(semiEntity.getMaterialCode()) // 半成品物料编码
                .setMaterialName(semiEntity.getMaterialName()) // 设置半成品物料名称
                .setOutsourcingProduct(null) // 外委产品置空
                .setOutsourcingProductName(null) // 外委产品名称置空
                .setEstimatedUsage(semiEntity.getEstimatedUsage())
                .setFormulaName(null) // 配方名称置空
                .setFormulaCode(null) // 配方编码置空
                .setTransferredAmount(null) // 已转量置空
                .setAvailableAmount(null) // 可转量置空
                .setOutsourcingAmount(null) // 外委量置空
                .setIsProduct(false) // 明确标识为半成品
                .setParentMaterialCode(parentProductDTO.getMaterialCode()) // 设置父产品物料编码
                .setRecipeRatio(semiEntity.getRecipeRatio()) // 配方比例
                .setExpanded(false)
                .setDirectCostId(semiEntity.getDirectCostId())
                .setPlanId(semiEntity.getPlanId()) // 设置项目计划ID
                .setPlanCode(planCode) // 设置计划编号
                .setItemType(semiEntity.getItemType())
                .setMaterialOut(null); // 外委产品选项置空
    }

    /**
     * 转换DTO列表为转外委实体列表
     */
    private List<OutsourcingTransferEntity> toEntityList(List<OutsourcingTransferItemDTO> dtoList) {
        if (dtoList == null || dtoList.isEmpty()) {
            return Collections.emptyList();
        }

        List<OutsourcingTransferEntity> entities = new ArrayList<>();
        for (OutsourcingTransferItemDTO dto : dtoList) {
            OutsourcingTransferEntity entity = toEntity(dto);
            if (entity != null) {
                entities.add(entity);
            }
        }

        return entities;
    }

    /**
     * 转换DTO为转外委实体
     */
    private OutsourcingTransferEntity toEntity(OutsourcingTransferItemDTO dto) {
        if (dto == null) {
            return null;
        }

        OutsourcingTransferEntity entity = new OutsourcingTransferEntity()
                .setPlanId(dto.getPlanId())
                .setMaterialCode(dto.getMaterialCode())
                .setOutsourcingProduct(dto.getOutsourcingProduct())
                .setEstimatedUsage(dto.getEstimatedUsage())
                .setFormulaName(dto.getFormulaName())
                .setFormulaCode(dto.getFormulaCode())
                .setTransferredAmount(dto.getTransferredAmount())
                .setAvailableAmount(dto.getAvailableAmount())
                .setOutsourcingAmount(dto.getOutsourcingAmount())
                .setIsProduct(dto.getIsProduct())
                .setParentMaterialCode(dto.getParentMaterialCode())
                .setRecipeRatio(dto.getRecipeRatio())
                .setDirectCostId(dto.getDirectCostId())
                .setItemType(dto.getItemType());

        // 转换子项目
        if (dto.getChildren() != null && !dto.getChildren().isEmpty()) {
            List<OutsourcingTransferEntity> children = new ArrayList<>();
            for (OutsourcingTransferItemDTO childDto : dto.getChildren()) {
                OutsourcingTransferEntity childEntity = toEntity(childDto);
                children.add(childEntity);
            }
            entity.setChildren(children);
        }

        return entity;
    }

    /**
     * 构建外委产品选项列表
     * 根据物料信息构建可选的外委产品选项
     */
    private List<OutsourcingTransferItemDTO.MaterialOutOption> buildMaterialOutOptions(OutsourcingTransferEntity entity) {
        List<OutsourcingTransferItemDTO.MaterialOutOption> options = new ArrayList<>();

        if (entity == null || !StringUtils.hasText(entity.getMaterialCode())) {
            return options;
        }

        // 添加自身作为外委产品选项
        OutsourcingTransferItemDTO.MaterialOutOption selfOption = new OutsourcingTransferItemDTO.MaterialOutOption();
        selfOption.setText(entity.getMaterialName() != null ? entity.getMaterialName() : entity.getMaterialCode());
        selfOption.setValue(entity.getMaterialCode());
        options.add(selfOption);

        // 可以根据业务需求添加其他外委产品选项
        // 例如：从配置表或其他服务获取相关的外委产品选项

        return options;
    }
}
